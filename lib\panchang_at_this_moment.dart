import 'dart:async';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/components/panchang_item_always_change.dart';
import 'package:panchang_at_this_moment/components/panchang_item_sometime_change.dart';
import 'package:panchang_at_this_moment/components/static_panchang_item.dart';

class PanchangAtTheMoment extends StatefulWidget {
  final DateTime startTime;
  final double lat;
  final double lng;
  final double alt;
  const PanchangAtTheMoment({
    super.key,
    required this.startTime,
    required this.lat,
    required this.lng,
    required this.alt,
  });

  @override
  State<PanchangAtTheMoment> createState() => _PanchangAtTheMomentState();
}

class _PanchangAtTheMomentState extends State<PanchangAtTheMoment> {
  PanchangDataForThreeDays? _panchangData;
  PanchangDataForThreeDays? translatedPanchangData;
  bool _isPlaying = true;
  late DateTime _currentTime = widget.startTime;
  late Timer _timer;
  late String currentTranslatedLanguage = Localizations.localeOf(context).languageCode;

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isPlaying) {
        setState(() {
          _currentTime = _currentTime.add(const Duration(seconds: 1));
        });
      }
    });
    _loadPanchangData();
  }

  Future<void> _loadPanchangData() async {
    final DateTime previousDay = widget.startTime.subtract(const Duration(days: 1));
    final DateTime nextDay = widget.startTime.add(const Duration(days: 1));

    final List<Future<PanchangData>> futures = [
      PanchangAPI.getPanchangData(previousDay, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(widget.startTime, widget.lat, widget.lng, widget.alt),
      PanchangAPI.getPanchangData(nextDay, widget.lat, widget.lng, widget.alt),
    ];

    final List<PanchangData> data = await Future.wait(futures);

    setState(() {
      _panchangData = PanchangDataForThreeDays(
        previousDay: data[0],
        currentDay: data[1],
        nextDay: data[2],
      );
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode),
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode),
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return panchangInfoBox(context, _currentTime);
  }

  Widget panchangInfoBox(BuildContext context, DateTime currentTime) {
    final TextStyle titleTextStyle = Theme.of(context).textTheme.titleLarge!;
    final TextStyle smallTextStyle = Theme.of(context).textTheme.bodySmall!;
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth > 1024;
    final isTablet = screenWidth > 600 && screenWidth <= 1024;
    final isMobile = screenWidth <= 600;
    
    // translation part:
    if (_panchangData != null && currentTranslatedLanguage != Localizations.localeOf(context).languageCode) {
      translatedPanchangData = PanchangDataForThreeDays(
        previousDay: _panchangData!.previousDay.translate(Localizations.localeOf(context).languageCode), 
        currentDay: _panchangData!.currentDay.translate(Localizations.localeOf(context).languageCode), 
        nextDay: _panchangData!.nextDay.translate(Localizations.localeOf(context).languageCode)
      );
      currentTranslatedLanguage = Localizations.localeOf(context).languageCode;
    }

    final Widget? panchangSomeTimeChangeBox = (translatedPanchangData != null)
        ? getPanchangItemSomeTimeChange(
            panchangData: translatedPanchangData!,
            currentTime: currentTime,
            titleTextStyle: titleTextStyle,
            smallTextStyle: smallTextStyle,
            context: context)
        : null;

    if (translatedPanchangData == null) {
      return Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFF3E5AB), // Warm cream color
              Colors.white,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const SizedBox(
                width: 50,
                height: 50,
                child: CircularProgressIndicator(
                  color: Color(0xFF6B4E3D),
                  strokeWidth: 3,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                "Loading Panchang data...",
                style: GoogleFonts.mallanna(
                  color: const Color(0xFF5D4037),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get all items to display
    final staticItems = getStaticPanchangItem(
      translatedPanchangData,
      DateTime(_currentTime.year, _currentTime.month, _currentTime.day),
      context,
    );
    
    final dynamicItems = getPanchangItemAlwayChange(
      panchangData: translatedPanchangData,
      context: context,
      currentTime: currentTime,
    );
    
    // Combine all items into a single list
    final allItems = [...staticItems, ...dynamicItems];

    // Time control widget
    Widget timeControlWidget = Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(color: const Color(0xFFD7CCC8)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFFF3E5AB),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.access_time_rounded,
                  color: Color(0xFF6B4E3D),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                DateFormat('dd-MM-yyyy HH:mm:ss').format(currentTime),
                style: GoogleFonts.dhurjati(
                  color: const Color(0xFF3E2723),
                  fontWeight: FontWeight.w600,
                  fontSize: isMobile ? 16 : (isTablet ? 18 : 20),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: _isPlaying ? const Color(0xFFFFEBEE) : const Color(0xFFE8F5E8),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  onPressed: () {
                    setState(() {
                      _isPlaying = !_isPlaying;
                      _currentTime = currentTime;
                    });
                  },
                  icon: Icon(
                    _isPlaying
                        ? Icons.pause_circle_filled_rounded
                        : Icons.play_circle_fill_rounded,
                    color: _isPlaying ? const Color(0xFFD32F2F) : const Color(0xFF388E3C),
                    size: 32,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xFFF3E5AB),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: IconButton(
                  icon: const Icon(
                    Icons.refresh_rounded,
                    color: Color(0xFF6B4E3D),
                    size: 28,
                  ),
                  onPressed: () {
                    setState(() {
                      _currentTime = DateTime.now();
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );

    // Main content with responsive grid
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFF3E5AB), // Warm cream color
            Colors.white,
          ],
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Time control widget
            timeControlWidget,
            
            // Main content
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Main Muhurta card (only if available)
                    if (panchangSomeTimeChangeBox != null)
                      Container(
                        width: double.infinity,
                        margin: EdgeInsets.symmetric(
    horizontal: isMobile ? 8 : 16, 
    vertical: isMobile ? 4 : 8
  ),
  padding: EdgeInsets.symmetric(
    horizontal: isMobile ? 12 : 20, 
    vertical: isMobile ? 12 : 16
  ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.08),
                              blurRadius: 12,
                              spreadRadius: 0,
                              offset: const Offset(0, 4),
                            ),
                          ],
                          border: Border.all(color: const Color(0xFFD7CCC8)),
                        ),
                        child: panchangSomeTimeChangeBox,
                      ),

                    // Responsive grid of cards
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: LayoutBuilder(
                        builder: (context, constraints) {
                          // Determine the number of cards per row based on screen width
                          int crossAxisCount;
                          if (isDesktop) {
                            crossAxisCount = 4; // 4 cards per row on desktop
                          } else if (isTablet) {
                            crossAxisCount = 3; // 3 cards per row on tablet
                          } else {
                            crossAxisCount = 1; // 1 card per row on mobile
                          }
                          
                          // Calculate item size
                          double itemWidth = (constraints.maxWidth / crossAxisCount) - 16;
                          
                          return Wrap(
                            spacing: 16.0,
                            runSpacing: 16.0,
                            alignment: WrapAlignment.center,
                            children: allItems.map((item) {
                              return Container(
                                width: itemWidth,
                                padding: const EdgeInsets.all(16.0),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(16.0),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.08),
                                      blurRadius: 12,
                                      spreadRadius: 0,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                  border: Border.all(color: const Color(0xFFD7CCC8)),
                                ),
                                child: item,
                              );
                            }).toList(),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}