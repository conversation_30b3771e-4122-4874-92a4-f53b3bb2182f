import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

List<Widget> getPanchangItemAlwayChange({
  required PanchangDataForThreeDays? panchangData,
  required DateTime currentTime,
  required BuildContext context
}) {
  final PanchangAlwaysChangeItem currentNakshatra =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.nakshatraList ?? [],
  panchangData?.currentDay.nakshatraList ?? [],
  panchangData?.nextDay.nakshatraList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentKarana =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.karanaList ?? [],
  panchangData?.currentDay.karanaList ?? [],
  panchangData?.nextDay.karanaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentTithiPaksha =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.tithiPakshaList ?? [],
  panchangData?.currentDay.tithiPakshaList ?? [],
  panchangData?.nextDay.tithiPakshaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentNakshatraPada =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.nakshatraPadaList ?? [],
  panchangData?.currentDay.nakshatraPadaList ?? [],
  panchangData?.nextDay.nakshatraPadaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentYoga =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.yogaList ?? [],
  panchangData?.currentDay.yogaList ?? [],
  panchangData?.nextDay.yogaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSuryaNakshatra =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.suryaNakshatraList ?? [],
  panchangData?.currentDay.suryaNakshatraList ?? [],
  panchangData?.nextDay.suryaNakshatraList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSuryaPada =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.suryaPadaList ?? [],
  panchangData?.currentDay.suryaPadaList ?? [],
  panchangData?.nextDay.suryaPadaList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentLunarRaasi =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.lunarRaasiList ?? [],
  panchangData?.currentDay.lunarRaasiList ?? [],
  panchangData?.nextDay.lunarRaasiList ?? []
].expand((x) => x).toList());

final PanchangAlwaysChangeItem currentSolarRaasi =
    _getCurrentNakshatra(currentTime, [
  panchangData?.previousDay.solarRaasiList ?? [],
  panchangData?.currentDay.solarRaasiList ?? [],
  panchangData?.nextDay.solarRaasiList ?? []
].expand((x) => x).toList());

  final String maasa = panchangData?.currentDay.maasa ?? '';
  final String samvatsara = panchangData?.currentDay.samvatsara ?? '';

  return [
    // Tithi Paksha Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: currentTithiPaksha.name,
          startTime: currentTithiPaksha.start,
          endTime: currentTithiPaksha.end,
        )
      ],
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.calendar_today, color: Colors.orange, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.tithiPaksha,
                      style: GoogleFonts.peddana(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF8D6E63),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentTithiPaksha.name,
                      style: GoogleFonts.mallanna(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF3E2723),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    
    // Karana Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.karana} : ${currentKarana.name}",
          startTime: currentKarana.start,
          endTime: currentKarana.end,
        )
      ],
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.indigo.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.access_time, color: Colors.indigo, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.karana,
                      style: GoogleFonts.peddana(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF8D6E63),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentKarana.name,
                      style: GoogleFonts.mallanna(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF3E2723),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    
    // Yoga Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.yoga} : ${currentYoga.name}",
          startTime: currentYoga.start,
          endTime: currentYoga.end,
        )
      ],
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.teal.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.autorenew, color: Colors.teal, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.yoga,
                      style: GoogleFonts.peddana(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF8D6E63),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      currentYoga.name,
                      style: GoogleFonts.mallanna(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF3E2723),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    
    // Maasa Card
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.amber.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(Icons.date_range, color: Colors.amber, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppLocalizations.of(context)!.maasa,
                    style: GoogleFonts.peddana(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF8D6E63),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    maasa,
                    style: GoogleFonts.mallanna(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF3E2723),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
    
    // Samvatsara Card
    Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.event, color: Colors.purple),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context)!.samvatsara,
                  style: GoogleFonts.peddana(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF8D6E63),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  samvatsara,
                  style: GoogleFonts.mallanna(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF3E2723),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    ),
    
    // Nakshatra Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.nakshatra} : ${currentNakshatra.name}",
          startTime: currentNakshatra.start,
          endTime: currentNakshatra.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.nakshatraPada} : ${currentNakshatraPada.name}",
          startTime: currentNakshatraPada.start,
          endTime: currentNakshatraPada.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.rashi} : ${currentLunarRaasi.name}",
          startTime: currentLunarRaasi.start,
          endTime: currentLunarRaasi.end,
        )
      ],
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blueGrey.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(Icons.nightlight_round, color: Colors.blueGrey, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    AppLocalizations.of(context)!.nakshatra,
                    style: GoogleFonts.peddana(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF3E2723),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.nakshatra,
                      value: currentNakshatra.name,
                    ),
                    const Divider(height: 16),
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.nakshatraPada,
                      value: currentNakshatraPada.name,
                    ),
                    const Divider(height: 16),
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.rashi,
                      value: currentLunarRaasi.name,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
    
    // Surya Nakshatra Card
    CountDownWithTimeDialog(
      currentTime: currentTime,
      showDateFormattedTime: true,
      countDownItems: [
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaNakshatra} : ${currentSuryaNakshatra.name}",
          startTime: currentSuryaNakshatra.start,
          endTime: currentSuryaNakshatra.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaNakshatraPada} : ${currentSuryaPada.name}",
          startTime: currentSuryaPada.start,
          endTime: currentSuryaPada.end,
        ),
        CountDownItem(
          title: "${AppLocalizations.of(context)!.suryaRashi} : ${currentSolarRaasi.name}",
          startTime: currentSolarRaasi.start,
          endTime: currentSolarRaasi.end,
        )
      ],
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(Icons.wb_sunny, color: Colors.orange, size: 20),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    AppLocalizations.of(context)!.suryaNakshatra,
                    style: GoogleFonts.peddana(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF3E2723),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.05),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.suryaNakshatra,
                      value: currentSuryaNakshatra.name,
                    ),
                    const Divider(height: 16),
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.suryaNakshatraPada,
                      value: currentSuryaPada.name,
                    ),
                    const Divider(height: 16),
                    _buildNakshatraInfoRow(
                      label: AppLocalizations.of(context)!.suryaRashi,
                      value: currentSolarRaasi.name,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ),
  ];
}

// Helper method to build consistent info rows
Widget _buildNakshatraInfoRow({required String label, required String value}) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Expanded(
        flex: 2,
        child: Text(
          label,
          style: GoogleFonts.peddana(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: const Color(0xFF8D6E63),
          ),
          overflow: TextOverflow.ellipsis,
        ),
      ),
      const SizedBox(width: 8),
      Expanded(
        flex: 3,
        child: Text(
          value,
          style: GoogleFonts.mallanna(
            fontWeight: FontWeight.w600,
            fontSize: 13,
            color: const Color(0xFF3E2723),
          ),
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.end,
        ),
      ),
    ],
  );
}

PanchangAlwaysChangeItem _getCurrentNakshatra(
    DateTime currentTime, List<PanchangAlwaysChangeItem> nakshatraList) {
  return nakshatraList.firstWhere(
      (element) =>
          element.start.isBefore(currentTime) &&
          element.end.isAfter(currentTime),
      orElse: () => PanchangAlwaysChangeItem(
            name: 'Failed to get data',
            start: DateTime.now(),
            end: DateTime.now(),
          ));
}