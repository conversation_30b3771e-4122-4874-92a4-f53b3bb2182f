import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/home_screen.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:panchang_at_this_moment/l10n/l10n.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
  });

  @override
  State<MyApp> createState() => MyAppState();

  static MyAppState? of(BuildContext context) =>
      context.findAncestorStateOfType<MyAppState>();
}

class MyAppState extends State<MyApp> {
  Locale _locale = const Locale('en');
  final _prefs = SharedPreferences.getInstance();

  @override
  void initState() {
    super.initState();
    _loadLocale();
  }

  Future<void> _loadLocale() async {
    final prefs = await _prefs;
    final localeCode = prefs.getString('locale');
    if (localeCode != null) {
      setState(() {
        _locale = Locale(localeCode);
      });
    }
  }

  void setLocale(Locale value) async {
    final prefs = await _prefs;
    prefs.setString('locale', value.languageCode);

    setState(() {
      _locale = value;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
        title: 'Panchang now',
        debugShowCheckedModeBanner: false,
        supportedLocales: L10n.all,
        locale: _locale,
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate
        ],
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF6B4E3D), // Warm brown color for spiritual theme
            brightness: Brightness.light,
          ),
          textTheme: TextTheme(
            // Headlines and titles - using Peddana
            displayLarge: GoogleFonts.peddana(
              fontSize: 32,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
            ),
            displayMedium: GoogleFonts.peddana(
              fontSize: 28,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
            ),
            displaySmall: GoogleFonts.peddana(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
            ),
            headlineLarge: GoogleFonts.peddana(
              fontSize: 22,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF5D4037),
            ),
            headlineMedium: GoogleFonts.peddana(
              fontSize: 20,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF5D4037),
            ),
            headlineSmall: GoogleFonts.peddana(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF5D4037),
            ),
            // Titles and labels - using Peddana
            titleLarge: GoogleFonts.peddana(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B4E3D),
            ),
            titleMedium: GoogleFonts.peddana(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF6B4E3D),
            ),
            titleSmall: GoogleFonts.peddana(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
            ),
            // Body text - using Mallanna
            bodyLarge: GoogleFonts.mallanna(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF3E2723),
            ),
            bodyMedium: GoogleFonts.mallanna(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF3E2723),
            ),
            bodySmall: GoogleFonts.mallanna(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF5D4037),
            ),
            // Labels - using Peddana
            labelLarge: GoogleFonts.peddana(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
            ),
            labelMedium: GoogleFonts.peddana(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
            ),
            labelSmall: GoogleFonts.peddana(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF8D6E63),
            ),
          ),
          cardTheme: CardTheme(
            elevation: 3,
            shadowColor: Colors.black.withValues(alpha: 0.1),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            color: Colors.white,
          ),
          appBarTheme: AppBarTheme(
            backgroundColor: const Color(0xFF6B4E3D),
            foregroundColor: Colors.white,
            titleTextStyle: GoogleFonts.peddana(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
        ),
        home: const HomeScreen());
  }
}
