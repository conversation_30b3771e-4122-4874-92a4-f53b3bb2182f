import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/data/panchang_stats.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CurrentPanchangItem {
  final String name;
  final DateTime start;
  final DateTime end;

  CurrentPanchangItem(
      {required this.end, required this.start, required this.name});
}

class CurrentItem{
  final String name;

  CurrentItem({required this.name});
}

Widget? getPanchangItemSomeTimeChange(  
    {required PanchangDataForThreeDays panchangData,
    required DateTime currentTime,
    required TextStyle titleTextStyle,
    required TextStyle smallTextStyle,
    required BuildContext context}) {
  final List<Widget> panchangItems = [];
  final List<CurrentItem> panchangSelectedItems = [];
  final List<CurrentPanchangItem> panchangSelectedItemsWithLocalizedNames = [];
  final panchangStats = getPanchangStats(context);
  
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (_isCurrent(dayData.bramhaMuhrat, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.bramhaMuhrat,
        style: titleTextStyle.copyWith(
            color: (panchangStats['bramhaMuhrat'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.bramhaMuhrat,
        start: dayData.bramhaMuhrat.start,
        end: dayData.bramhaMuhrat.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'bramhaMuhrat'));
    }
    if (dayData.abhijit != null && _isCurrent(dayData.abhijit!, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.abhijit,
        style: titleTextStyle.copyWith(
            color: (panchangStats['abhijit'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.abhijit,
        start: dayData.abhijit!.start,
        end: dayData.abhijit!.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'abhijit'));
    }
    if (_isCurrent(dayData.godhuli, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.godhuli,
        style: titleTextStyle.copyWith(
            color: (panchangStats['godhuli'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.godhuli,
        start: dayData.godhuli.start,
        end: dayData.godhuli.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'godhuli'));
    }
    if (_isCurrent(dayData.pratahSandhya, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.pratahSandhya,
        style: titleTextStyle.copyWith(
            color: (panchangStats['pratahSandhya'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.pratahSandhya,
        start: dayData.pratahSandhya.start,
        end: dayData.pratahSandhya.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'pratahSandhya'));
    }
    if (_isCurrent(dayData.vijayMuhurat, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.vijayMuhurat,        style: titleTextStyle.copyWith(
            color: (panchangStats['vijayMuhurat'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.vijayMuhurat,
        start: dayData.vijayMuhurat.start,
        end: dayData.vijayMuhurat.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'vijayMuhurat'));
    }
    if (_isCurrent(dayData.sayahnaSandhya, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.sayahnaSandhya,
        style: titleTextStyle.copyWith(
            color: (panchangStats['sayahnaSandhya'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.sayahnaSandhya,
        start: dayData.sayahnaSandhya.start,
        end: dayData.sayahnaSandhya.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'sayahnaSandhya'));
    }
    if (_isCurrent(dayData.nishitaMuhurta, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.nishitaMuhurta,
        style: titleTextStyle.copyWith(
            color: (panchangStats['nishitaMuhurta'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.nishitaMuhurta,
        start: dayData.nishitaMuhurta.start,
        end: dayData.nishitaMuhurta.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'nishitaMuhurta'));
    }
    if (_isCurrent(dayData.rahuKal, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.rahuKal,
        style: titleTextStyle.copyWith(
            color: (panchangStats['rahuKal'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.rahuKal,
        start: dayData.rahuKal.start,
        end: dayData.rahuKal.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'rahuKal'));
    }
    if (_isCurrent(dayData.gulikaiKal, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.gulikaiKal,
        style: titleTextStyle.copyWith(
            color: (panchangStats['gulikaiKal'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.gulikaiKal,
        start: dayData.gulikaiKal.start,
        end: dayData.gulikaiKal.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'gulikaiKal'));
    }
    if (_isCurrent(dayData.yamaganda, currentTime)) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.yamaganda,
        style: titleTextStyle.copyWith(
            color: (panchangStats['yamaganda'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.yamaganda,
        start: dayData.yamaganda.start,
        end: dayData.yamaganda.end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'yamaganda'));
    }
    if (dayData.durMuhurtam
        .any((element) => _isCurrent(element, currentTime))) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.durMuhurtam,
        style: titleTextStyle.copyWith(
            color: (panchangStats['durMuhurtam'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.durMuhurtam,
        start: dayData.durMuhurtam
            .firstWhere((element) => _isCurrent(element, currentTime))
            .start,
        end: dayData.durMuhurtam
            .firstWhere((element) => _isCurrent(element, currentTime))
            .end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'durMuhurtam'));
    }
    if (dayData.varjyam.any((element) => _isCurrent(element, currentTime))) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.varjyam,
        style: titleTextStyle.copyWith(
            color: (panchangStats['varjyam'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.varjyam,
        start: dayData.varjyam
            .firstWhere((element) => _isCurrent(element, currentTime))
            .start,
        end: dayData.varjyam
            .firstWhere((element) => _isCurrent(element, currentTime))
            .end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'varjyam'));
    }
    if (dayData.amritKal.any((element) => _isCurrent(element, currentTime))) {
      panchangItems.add(Text(
        AppLocalizations.of(context)!.amritKal,
        style: titleTextStyle.copyWith(
            color: (panchangStats['amritKal'] as Map)['isPositive']
                ? Colors.green
                : Colors.red),
      ));
      panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
        name: AppLocalizations.of(context)!.amritKal,
        start: dayData.amritKal
            .firstWhere((element) => _isCurrent(element, currentTime))
            .start,
        end: dayData.amritKal
            .firstWhere((element) => _isCurrent(element, currentTime))
            .end,
      ));
      panchangSelectedItems.add(CurrentItem(
          name: 'amritKal'));
    }
  }

  List<String> panchangTips = panchangSelectedItems
      .map((item) =>
          ((panchangStats[item.name] as Map)["displayMessage"] as String))
      .toList();

  List<double> goodList = panchangSelectedItems
      .where(
          (item) => ((panchangStats[item.name] as Map)["isPositive"] as bool))
      .map((item) => ((panchangStats[item.name] as Map)["score"] as double))
      .toList();
  double good = (goodList.isEmpty) ? 0 : goodList.reduce((a, b) => a + b);

  List<double> badList = panchangSelectedItems
      .where((item) => !(panchangStats[item.name] as Map)["isPositive"])
      .map((item) => ((panchangStats[item.name] as Map)["score"] as double))
      .toList();
  double bad = (badList.isEmpty) ? 0 : badList.reduce((a, b) => a + b);

  double total = good + bad * -1;
  double goodRatio = (good / total) * 100;
  double badRatio = (bad * -1 / total) * 100;

  return (panchangItems.isNotEmpty)
      ? CountDownWithTimeDialog(
          showMinutesFormattedTime: true,
          currentTime: currentTime,
          countDownItems: panchangSelectedItemsWithLocalizedNames
              .map((item) => CountDownItem(
                    title: item.name,
                    endTime: item.end,
                    startTime: item.start,
                  ))
              .toList(),
          child: Container(
            color: Colors.transparent,
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: panchangItems,
                      ),
                      Column(
                        children: [
                          Text(
                            "+ve score : ${bad + good}",
                            style: smallTextStyle,
                          ),
                          const SizedBox(
                            height: 2,
                          ),
                          GoodAndBadCircle(
                            good: goodRatio,
                            bad: badRatio,
                          ),
                        ],
                      ),
                    ]),
                const SizedBox(
                  height: 10,
                ),
                CirculatingText(
                  texts: panchangTips,
                )
              ],
            ),
          ))
      : null;
}

bool _isCurrent(PanchangSomeTimeItem interval, DateTime currentTime) {
  return interval.start.isBefore(currentTime) &&
      interval.end.isAfter(currentTime);
}
