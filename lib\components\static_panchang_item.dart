import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SunMoonTimeRow extends StatelessWidget {
  final IconData icon;
  final String title;
  final DateTime? rise;
  final DateTime? set;

  const SunMoonTimeRow(
      {super.key,
      required this.icon,
      required this.title,
      this.rise,
      this.set});

  Widget _buildTime(String label, DateTime? time, Color iconColor) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: GoogleFonts.peddana(
              fontSize: 8,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF5D4037),
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
          const SizedBox(height: 1),
          time == null
              ? const SizedBox(
                  width: 10,
                  height: 10,
                  child: CircularProgressIndicator(strokeWidth: 1),
                )
              : Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      label.toLowerCase().contains('rise') ? Icons.arrow_upward : Icons.arrow_downward,
                      size: 8,
                      color: iconColor,
                    ),
                    Text(
                      timeFormatter(time),
                      style: GoogleFonts.mallanna(
                        fontSize: 7,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF3E2723),
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Color iconColor = title == 'Sun' ? Colors.orange : Colors.indigo;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 1),
      padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: title == 'Sun'
              ? [const Color(0xFFFFF3E0), const Color(0xFFFFE0B2)]
              : [const Color(0xFFE8EAF6), const Color(0xFFE1BEE7)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.02),
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 12,
                color: iconColor,
              ),
              const SizedBox(width: 2),
              Text(
                title == 'Sun' ? 'Sun' : 'Moon',
                style: GoogleFonts.peddana(
                  fontSize: 9,
                  fontWeight: FontWeight.w600,
                  color: iconColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: _buildTime(
                  title=='Sun' ? AppLocalizations.of(context)!.sunrise : AppLocalizations.of(context)!.moonRise,
                  rise,
                  iconColor,
                ),
              ),
              const SizedBox(width: 2),
              Expanded(
                child: _buildTime(
                  title=='Sun' ? AppLocalizations.of(context)!.sunset : AppLocalizations.of(context)!.moonSet,
                  set,
                  iconColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

List<Widget> getStaticPanchangItem(PanchangDataForThreeDays? panchangData, DateTime currentTime, BuildContext context) {
  if (panchangData == null) {
    return const [
      Center(child: CircularProgressIndicator()),
    ];
  }

  // The logic remains exactly the same as before
  DateTime? sunrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.sunrise.isAfter(currentTime) &&
        sunrise == null) {
      sunrise = dayData.sunrise;
      break;
    }
  }

  DateTime? sunset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (sunrise != null && dayData.sunset.isAfter(sunrise)) {
      sunset = dayData.sunset;
      break;
    }
  }

  DateTime? moonrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.moonrise.isAfter(currentTime.add(const Duration(hours: 3))) &&
        moonrise == null) {
      moonrise = dayData.moonrise;
      break;
    }
  }

  DateTime? moonset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (moonrise != null && dayData.moonset.isAfter(moonrise)) {
      moonset = dayData.moonset;
      break;
    }
  }

  return [
    SunMoonTimeRow(
      title: 'Sun',
      icon: Icons.wb_sunny,
      rise: sunrise,
      set: sunset,
    ),
    SunMoonTimeRow(
      title: 'Moon',
      icon: Icons.nightlight_round,
      rise: moonrise,
      set: moonset,
    ),
    Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${AppLocalizations.of(context)!.week} : ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            panchangData.currentDay.weekday,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 12),
          const Icon(
            Icons.calendar_month,
            size: 20,
            color: Colors.brown,
          ),
        ],
      ),
    ),
  ];
}