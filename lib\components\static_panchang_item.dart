import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class SunMoonTimeRow extends StatelessWidget {
  final IconData icon;
  final String title;
  final DateTime? rise;
  final DateTime? set;

  const SunMoonTimeRow(
      {super.key,
      required this.icon,
      required this.title,
      this.rise,
      this.set});

  Widget _buildTime(String label, DateTime? time, Color iconColor) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Text(
            label,
            style: GoogleFonts.peddana(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF3E2723),
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 10),
          time == null
              ? const CircularProgressIndicator()
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      label.toLowerCase().contains('rise') ? Icons.arrow_upward : Icons.arrow_downward,
                      size: 16,
                      color: iconColor,
                    ),
                    const SizedBox(width: 5),
                    Text(
                      timeFormatter(time),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final Color iconColor = title == 'Sun' ? Colors.orange : Colors.indigo;
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: title == 'Sun' 
              ? [Colors.blue.shade50, Colors.orange.shade50]
              : [Colors.indigo.shade50, Colors.purple.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 28,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Text(
                title == 'Sun' ? 'Sun Time' : 'Moon Time',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: iconColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildTime(
                title=='Sun' ? AppLocalizations.of(context)!.sunrise : AppLocalizations.of(context)!.moonRise, 
                rise,
                iconColor,
              ),
              _buildTime(
                title=='Sun' ? AppLocalizations.of(context)!.sunset : AppLocalizations.of(context)!.moonSet, 
                set,
                iconColor,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

List<Widget> getStaticPanchangItem(PanchangDataForThreeDays? panchangData, DateTime currentTime, BuildContext context) {
  if (panchangData == null) {
    return const [
      Center(child: CircularProgressIndicator()),
    ];
  }

  // The logic remains exactly the same as before
  DateTime? sunrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.sunrise.isAfter(currentTime) &&
        sunrise == null) {
      sunrise = dayData.sunrise;
      break;
    }
  }

  DateTime? sunset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (sunrise != null && dayData.sunset.isAfter(sunrise)) {
      sunset = dayData.sunset;
      break;
    }
  }

  DateTime? moonrise;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (dayData.moonrise.isAfter(currentTime.add(const Duration(hours: 3))) &&
        moonrise == null) {
      moonrise = dayData.moonrise;
      break;
    }
  }

  DateTime? moonset;
  for (PanchangData dayData in [
    panchangData.previousDay,
    panchangData.currentDay,
    panchangData.nextDay
  ]) {
    if (moonrise != null && dayData.moonset.isAfter(moonrise)) {
      moonset = dayData.moonset;
      break;
    }
  }

  return [
    SunMoonTimeRow(
      title: 'Sun',
      icon: Icons.wb_sunny,
      rise: sunrise,
      set: sunset,
    ),
    SunMoonTimeRow(
      title: 'Moon',
      icon: Icons.nightlight_round,
      rise: moonrise,
      set: moonset,
    ),
    Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${AppLocalizations.of(context)!.week} : ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            panchangData.currentDay.weekday,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 12),
          const Icon(
            Icons.calendar_month,
            size: 20,
            color: Colors.brown,
          ),
        ],
      ),
    ),
  ];
}